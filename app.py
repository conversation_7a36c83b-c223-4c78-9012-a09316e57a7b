import streamlit as st
import replicate
import subprocess
import tempfile
import os
import time
import logging
import backoff
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load Replicate API token
try:
    REPLICATE_TOKEN = st.secrets["api"]["replicate_token"]
    # Initialize client with a longer timeout
    client = replicate.Client(api_token=REPLICATE_TOKEN, timeout=900)  # 15 minute timeout

    # Test the API token with a simple API call
    try:
        # Make a simple API call to verify the token works
        # Just check if we can access the model we need
        model = client.models.get("victor-upmeet/whisperx")
        logging.info(f"Replicate API token verified successfully. Model: {model.name}")
    except Exception as token_error:
        logging.error(f"API token verification failed: {str(token_error)}")
        raise Exception(f"API token verification failed: {str(token_error)}")

    logging.info("Replicate client initialized successfully")
except Exception as e:
    error_msg = f"Error loading Replicate API token: {str(e)}"
    logging.error(error_msg)
    st.error(error_msg)
    st.info("Please ensure you have a valid Replicate API token in .streamlit/secrets.toml")
    REPLICATE_TOKEN = None
    client = None

# ----------------------------
# 1. Audio Preprocessing
# ----------------------------
def check_ffmpeg_availability():
    """
    Check if ffmpeg and ffprobe are available on the system.
    Returns True if both are available, False otherwise.
    """
    try:
        # Check ffmpeg
        ffmpeg_result = subprocess.run(["ffmpeg", "-version"],
                                     capture_output=True, text=True, timeout=10)
        # Check ffprobe
        ffprobe_result = subprocess.run(["ffprobe", "-version"],
                                      capture_output=True, text=True, timeout=10)
        return ffmpeg_result.returncode == 0 and ffprobe_result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
        return False

def preprocess_video_to_wav(uploaded_file):
    """
    Extracts audio from an uploaded MP4, downmixes to mono, resamples to 16 kHz,
    and applies advanced audio processing via ffmpeg including:
    - Noise reduction with high-pass and low-pass filtering
    - Loudness normalization
    - Dynamic audio normalization for better speech clarity

    Returns the path to the processed WAV file.
    """
    # Check if ffmpeg is available
    if not check_ffmpeg_availability():
        error_msg = """
        FFmpeg is not available on this system. This is required for audio processing.

        **For Streamlit Cloud deployment:**
        - Make sure you have a `packages.txt` file with `ffmpeg` listed
        - Redeploy your app after adding the packages.txt file

        **For local development:**
        - Install ffmpeg: https://ffmpeg.org/download.html
        - On Windows: Download from https://www.gyan.dev/ffmpeg/builds/
        - On macOS: `brew install ffmpeg`
        - On Ubuntu/Debian: `sudo apt install ffmpeg`
        """
        st.error(error_msg)
        logging.error("FFmpeg not found on system")
        return None
    # Save upload to temp MP4
    input_path = tempfile.NamedTemporaryFile(suffix=".mp4", delete=False).name
    with open(input_path, "wb") as f:
        f.write(uploaded_file.getbuffer())

    # Prepare output WAV
    output_path = tempfile.NamedTemporaryFile(suffix=".wav", delete=False).name

    logging.info(f"Processing video file: {input_path} to WAV: {output_path}")

    try:
        # Check if enhanced audio processing is enabled
        enable_enhanced_audio = st.session_state.get('enable_enhanced_audio', True)

        if enable_enhanced_audio:
            # Enhanced audio pre-processing with simplified, compatible filters
            # - highpass: removes low frequency noise below 80Hz
            # - lowpass: removes high frequency noise above 8000Hz
            # - loudnorm: normalizes audio loudness for consistent levels
            # Removed afftdn and complex silenceremove for better compatibility
            result = subprocess.run([
                "ffmpeg", "-y", "-i", input_path,
                "-ac", "1",    # mono
                "-ar", "16000",# 16 kHz - optimal for Whisper
                "-filter:a", "highpass=f=80,lowpass=f=8000,loudnorm",
                "-c:a", "pcm_s16le",  # Uncompressed PCM for best quality
                output_path
            ], check=True, capture_output=True, text=True)

            logging.info("Enhanced audio pre-processing with simplified filters completed successfully")
        else:
            # Basic audio processing without filters
            result = subprocess.run([
                "ffmpeg", "-y", "-i", input_path,
                "-ac", "1",    # mono
                "-ar", "16000",# 16 kHz - optimal for Whisper
                "-c:a", "pcm_s16le",  # Uncompressed PCM for best quality
                output_path
            ], check=True, capture_output=True, text=True)

            logging.info("Basic audio processing completed successfully")

        # Check if the output file exists and has content
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            file_size_mb = os.path.getsize(output_path)/1024/1024
            logging.info(f"Successfully created WAV file: {output_path}, size: {file_size_mb:.2f} MB")
            return output_path
        else:
            logging.error(f"WAV file creation failed or file is empty: {output_path}")
            st.error("Audio preprocessing failed. The output file is empty or was not created.")
            return None

    except subprocess.CalledProcessError as e:
        logging.error(f"Enhanced FFmpeg processing failed: {e.stderr}")

        # Check for memory allocation errors and try simplified processing
        if "Cannot allocate memory" in str(e.stderr) or "memory" in str(e.stderr).lower():
            logging.warning("Memory allocation error detected, trying simplified audio processing")
            st.warning("Enhanced audio processing failed due to memory constraints. Trying simplified approach...")

            try:
                # Simplified processing without advanced noise reduction
                result = subprocess.run([
                    "ffmpeg", "-y", "-i", input_path,
                    "-ac", "1",    # mono
                    "-ar", "16000",# 16 kHz
                    "-filter:a", "highpass=f=80,lowpass=f=8000,loudnorm",  # Simplified filter chain
                    "-c:a", "pcm_s16le",
                    output_path
                ], check=True, capture_output=True, text=True)

                logging.info("Simplified audio processing completed successfully")

                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    file_size_mb = os.path.getsize(output_path)/1024/1024
                    logging.info(f"Successfully created WAV file with simplified processing: {output_path}, size: {file_size_mb:.2f} MB")
                    return output_path
                else:
                    # If simplified processing also fails, try minimal processing
                    logging.warning("Simplified processing failed, trying minimal approach")
                    result = subprocess.run([
                        "ffmpeg", "-y", "-i", input_path,
                        "-ac", "1",    # mono
                        "-ar", "16000",# 16 kHz
                        "-c:a", "pcm_s16le",  # Just convert to optimal format
                        output_path
                    ], check=True, capture_output=True, text=True)

                    if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                        file_size_mb = os.path.getsize(output_path)/1024/1024
                        logging.info(f"Successfully created WAV file with minimal processing: {output_path}, size: {file_size_mb:.2f} MB")
                        st.info("Used minimal audio processing due to system constraints.")
                        return output_path
                    else:
                        logging.error(f"All audio processing approaches failed")
                        st.error("Audio preprocessing failed with all approaches.")
                        return None

            except subprocess.CalledProcessError as e2:
                logging.error(f"Simplified FFmpeg processing also failed: {e2.stderr}")
                st.error("Audio preprocessing failed. Ensure ffmpeg is installed.")
                st.error(f"FFmpeg error details: {e2.stderr}")
                return None
        else:
            st.error("Audio preprocessing failed. Ensure ffmpeg is installed.")
            st.error(f"FFmpeg error details: {e.stderr}")
            return None
    except Exception as e:
        logging.error(f"Unexpected error in audio preprocessing: {str(e)}")
        st.error(f"Audio preprocessing failed with error: {str(e)}")
        return None
    finally:
        # Clean up the input file
        if os.path.exists(input_path):
            os.remove(input_path)
            logging.info(f"Removed temporary input file: {input_path}")

# ----------------------------
# 2. Audio File Chunking for Large Files
# ----------------------------
def split_audio_file(wav_path, max_chunk_size_mb=50, output_dir=None, overlap_seconds=2.0):
    """
    Splits a large audio file into smaller chunks that can be processed by the API.
    Includes overlap between chunks to prevent sentence cutting at chunk boundaries.
    Returns a list of paths to the chunk files in chronological order with their time ranges.

    Args:
        wav_path: Path to the WAV file to split
        max_chunk_size_mb: Maximum size of each chunk in MB
        output_dir: Directory to save chunks (if None, uses system temp dir)
        overlap_seconds: Amount of overlap between chunks in seconds to prevent sentence cutting

    Returns:
        List of tuples (chunk_path, start_time, duration) in chronological order
    """
    # Check if file needs splitting
    file_size_mb = os.path.getsize(wav_path) / (1024 * 1024)

    if file_size_mb <= max_chunk_size_mb:
        logging.info(f"Audio file size ({file_size_mb:.2f} MB) is within limits, no splitting needed")

        # Get the duration of the file
        try:
            result = subprocess.run([
                "ffprobe", "-v", "error", "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1", wav_path
            ], capture_output=True, text=True, check=True)

            duration = float(result.stdout.strip())
            # Return the original file with its time range (0 to duration)
            return [(wav_path, 0.0, duration)]
        except:
            # If we can't get the duration, just return the file
            return [(wav_path, 0.0, 0.0)]

    # File needs to be split
    logging.info(f"Audio file size ({file_size_mb:.2f} MB) exceeds {max_chunk_size_mb} MB limit, splitting into chunks")

    # Get audio duration using ffprobe
    try:
        result = subprocess.run([
            "ffprobe", "-v", "error", "-show_entries", "format=duration",
            "-of", "default=noprint_wrappers=1:nokey=1", wav_path
        ], capture_output=True, text=True, check=True)

        duration = float(result.stdout.strip())
        logging.info(f"Audio duration: {duration:.2f} seconds")

        # Calculate number of chunks needed
        num_chunks = int(file_size_mb / max_chunk_size_mb) + 1
        chunk_duration = duration / num_chunks

        logging.info(f"Splitting into {num_chunks} chunks of approximately {chunk_duration:.2f} seconds each")

        # Create chunks in chronological order with overlap
        chunk_info = []
        for i in range(num_chunks):
            # Calculate start time with overlap (except for first chunk)
            start_time = i * chunk_duration
            if i > 0:
                # Add overlap to previous chunks
                start_time -= overlap_seconds
                # Ensure start_time is not negative
                start_time = max(0, start_time)

            # For the last chunk, ensure we get to the end of the file
            if i == num_chunks - 1:
                duration_arg = duration - start_time
            else:
                # Add overlap to the end of each chunk (except the last one)
                duration_arg = chunk_duration + overlap_seconds
                # Ensure we don't go beyond the file duration
                if start_time + duration_arg > duration:
                    duration_arg = duration - start_time

            # Create output path for this chunk with a sequential number to ensure order
            # Use a fixed format for the chunk number to ensure proper sorting
            chunk_path = tempfile.NamedTemporaryFile(suffix=f"_chunk{i+1:03d}.wav", delete=False).name

            # Run ffmpeg to extract the chunk
            subprocess.run([
                "ffmpeg", "-y", "-i", wav_path,
                "-ss", str(start_time), "-t", str(duration_arg),
                "-c:a", "pcm_s16le", "-ar", "16000", "-ac", "1",
                chunk_path
            ], check=True, capture_output=True)

            logging.info(f"Created chunk with overlap: time range {start_time:.2f}s - {start_time + duration_arg:.2f}s (includes {overlap_seconds:.1f}s overlap)")

            chunk_size_mb = os.path.getsize(chunk_path) / (1024 * 1024)
            logging.info(f"Created chunk {i+1}/{num_chunks}: {chunk_path}, size: {chunk_size_mb:.2f} MB, time range: {start_time:.2f}s - {start_time + duration_arg:.2f}s")

            # Store the chunk path along with its time range
            chunk_info.append((chunk_path, start_time, duration_arg))

        # Sort chunks by start time to ensure chronological order
        chunk_info.sort(key=lambda x: x[1])

        # Log the chunks in order
        for i, (path, start, dur) in enumerate(chunk_info):
            logging.info(f"Chunk {i+1} in sequence: {path}, time range: {start:.2f}s - {start + dur:.2f}s")

        return chunk_info

    except subprocess.CalledProcessError as e:
        logging.error(f"Error getting audio duration: {e.stderr}")
        # If we can't split properly, return the original file
        return [(wav_path, 0.0, 0.0)]
    except Exception as e:
        logging.error(f"Unexpected error in audio splitting: {str(e)}")
        return [(wav_path, 0.0, 0.0)]

# ----------------------------
# 3. Transcription via Replicate Python client
# ----------------------------
# Define backoff parameters for retry mechanism
@backoff.on_exception(
    backoff.expo,
    (replicate.exceptions.ReplicateError, ConnectionError, TimeoutError, Exception),
    max_tries=5,  # Increased from 3 to 5
    max_time=600,  # 10 minutes max total time (increased from 5 minutes)
    jitter=backoff.full_jitter,  # Add jitter to avoid thundering herd
    factor=2,  # Exponential backoff factor
    on_backoff=lambda details: logging.info(f"Retrying API call (attempt {details['tries']}/{5})...")
)
def run_replicate_model(model_url, input_params):
    """
    Helper function to run a Replicate model with retry logic
    """
    logging.info(f"Running model: {model_url}")

    # Create a clean copy of the parameters
    clean_params = {}

    # Copy all parameters except None values
    for key, value in input_params.items():
        if value is not None:
            clean_params[key] = value

    # Log the actual parameters being sent to the API
    param_log = {k: v for k, v in clean_params.items() if k != 'audio_file'}

    # Log subtitle formatting parameters specifically
    subtitle_params = {
        k: v for k, v in param_log.items()
        if k in ['max_line_width', 'max_line_count', 'max_line_length', 'max_words_per_line',
                'max_words_per_segment', 'segment_text_line_max_chars']
    }

    logging.info(f"Sending parameters to API: {param_log}")
    logging.info(f"Subtitle formatting parameters: {subtitle_params}")

    try:
        # Set a longer timeout for the API call
        # For the WhisperX model, we need to pass the audio file directly in the input
        return client.run(
            model_url,
            input=clean_params,
            timeout=900  # 15 minutes timeout
        )
    except Exception as e:
        logging.error(f"Error in run_replicate_model: {str(e)}")
        raise

def transcribe_with_local_upload(wav_path):
    """
    Uses the replicate Python client to upload the WAV file and run Whisper.
    Includes retry logic and better error handling.
    For large files, splits into chunks and processes each chunk separately.
    """
    logging.info(f"Starting transcription for file: {wav_path}")

    # Check file size and log it
    file_size_mb = os.path.getsize(wav_path) / (1024 * 1024)
    logging.info(f"Audio file size: {file_size_mb:.2f} MB")

    # Split the audio file into chunks if it's too large
    # Replicate API has a limit of 100MB for file uploads
    MAX_CHUNK_SIZE_MB = 100
    audio_chunk_info = split_audio_file(wav_path, max_chunk_size_mb=MAX_CHUNK_SIZE_MB)

    if len(audio_chunk_info) > 1:
        logging.info(f"Processing audio in {len(audio_chunk_info)} chunks")
        st.info(f"File is large ({file_size_mb:.1f} MB). Processing in {len(audio_chunk_info)} chunks...")

        # Process each chunk and combine results
        all_segments = []
        all_word_segments = []
        chunk_results = []

        progress_bar = st.progress(0)

        # First, process all chunks and store their results with their time info
        for i, (chunk_path, start_time, duration) in enumerate(audio_chunk_info):
            chunk_progress_text = st.empty()
            chunk_progress_text.text(f"Processing chunk {i+1} of {len(audio_chunk_info)} (time: {start_time:.2f}s - {start_time + duration:.2f}s)...")

            # Process this chunk
            chunk_result = process_audio_chunk(chunk_path)

            if chunk_result:
                # Store the result for this chunk along with its time info
                chunk_results.append((chunk_result, start_time, duration))
                logging.info(f"Successfully processed chunk {i+1}/{len(audio_chunk_info)} (time: {start_time:.2f}s - {start_time + duration:.2f}s)")
            else:
                logging.error(f"Failed to process chunk {i+1} (time: {start_time:.2f}s - {start_time + duration:.2f}s)")
                st.error(f"Failed to process chunk {i+1}. The transcription may be incomplete.")

            # Update progress
            progress_bar.progress((i + 1) / len(audio_chunk_info))

            # Clean up this chunk if it's not the original file
            if chunk_path != wav_path:
                try:
                    os.remove(chunk_path)
                    logging.info(f"Removed temporary chunk file: {chunk_path}")
                except Exception as e:
                    logging.error(f"Error removing chunk file: {str(e)}")

        # Now combine the results in the correct order based on the original time ranges
        combined_text = ""

        # Sort chunk results by their start time to ensure correct order
        chunk_results.sort(key=lambda x: x[1])

        for i, (chunk_result, start_time, duration) in enumerate(chunk_results):
            logging.info(f"Processing result for chunk {i+1} (time: {start_time:.2f}s - {start_time + duration:.2f}s)")

            if 'segments' in chunk_result and chunk_result.get('segments'):
                segments = chunk_result.get('segments', [])

                # Skip empty segments
                if not segments:
                    logging.warning(f"Chunk {i+1} has no segments, skipping")
                    continue

                # Adjust timestamps for this chunk's segments based on the actual start time
                for seg in segments:
                    # Get the original timestamps relative to this chunk
                    original_start = seg.get('start', 0)
                    original_end = seg.get('end', 0)

                    # Adjust timestamps based on the actual start time of this chunk in the full audio
                    seg['start'] = start_time + original_start
                    seg['end'] = start_time + original_end

                    # Clean the transcription text to fix common errors
                    if 'text' in seg:
                        seg['text'] = clean_transcription_text(seg['text'])

                    # Add to the combined segments list
                    all_segments.append(seg)

                    # Add to combined text
                    combined_text += seg.get('text', '') + " "

                # Handle word segments if available
                if 'word_segments' in chunk_result:
                    for word_seg in chunk_result.get('word_segments', []):
                        # Adjust timestamps for word segments
                        original_word_start = word_seg.get('start', 0)
                        original_word_end = word_seg.get('end', 0)

                        word_seg['start'] = start_time + original_word_start
                        word_seg['end'] = start_time + original_word_end

                        all_word_segments.append(word_seg)

                logging.info(f"Added {len(segments)} segments from chunk {i+1}")
            else:
                logging.warning(f"Chunk {i+1} has no segments data")

        # Clear progress indicators
        progress_bar.empty()
        chunk_progress_text.empty()

        # Sort segments by start time to ensure correct order
        all_segments.sort(key=lambda x: x.get('start', 0))
        if all_word_segments:
            all_word_segments.sort(key=lambda x: x.get('start', 0))

        # Filter out any hallucinated segments with comprehensive detection
        # Apply more conservative filtering to preserve legitimate dialogue
        debug_mode = st.session_state.get('debug_hallucination_filter', False)
        filtered_segments = filter_hallucinated_segments(all_segments, min_segment_duration=0.1, min_confidence=0.2, debug_mode=debug_mode)
        if len(filtered_segments) != len(all_segments):
            logging.info(f"Filtered out {len(all_segments) - len(filtered_segments)} potentially hallucinated segments")
            all_segments = filtered_segments

            # Regenerate the combined text from the filtered segments
            combined_text = " ".join(seg.get('text', '') for seg in all_segments)

        # Create a combined result dictionary
        combined_result = {
            'text': combined_text.strip(),
            'segments': all_segments
        }

        if all_word_segments:
            combined_result['word_segments'] = all_word_segments

        logging.info(f"Completed processing all {len(audio_chunk_info)} chunks")
        return combined_result
    else:
        # Process single file normally - there's only one chunk
        chunk_path, start_time, duration = audio_chunk_info[0]
        result = process_audio_chunk(chunk_path)

        # Apply text cleaning to single-chunk results
        if result and 'segments' in result:
            for seg in result['segments']:
                if 'text' in seg:
                    seg['text'] = clean_transcription_text(seg['text'])

        # If this is not the original file (unlikely but possible), clean it up
        if chunk_path != wav_path:
            try:
                os.remove(chunk_path)
                logging.info(f"Removed temporary chunk file: {chunk_path}")
            except Exception as e:
                logging.error(f"Error removing chunk file: {str(e)}")

        return result


def process_audio_chunk(wav_path):
    """
    Process a single audio chunk with the WhisperX model.
    Tries primary model first, then falls back to alternative model if needed.
    """
    # Primary model - using WhisperX model with correct API parameters
    primary_model = "victor-upmeet/whisperx:84d2ad2d6194fe98a17d2b60bef1c7f910c46b2f6fd38996ca457afd9c8abfcb"
    primary_params = {
        "audio_file": None,  # Will be set later with correct parameter name
        "language": "en",  # Force English language

        # Core transcription parameters - optimized for quality
        "batch_size": 32,  # Smaller batch size for better accuracy
        "temperature": 0,  # Sampling randomness (0 = deterministic)

        # Enhanced initial prompt for better context recognition and accuracy
        "initial_prompt": "This is professional dialogue from a high-quality TV drama with clear speech, proper names, and natural conversation. Transcribe all speech including quiet dialogue at the beginning and end.",

        # Voice Activity Detection parameters - optimized to capture quiet dialogue
        "vad_onset": st.session_state.get('vad_onset', 0.3),   # Lower threshold to catch quiet speech
        "vad_offset": st.session_state.get('vad_offset', 0.4),  # Higher threshold to avoid cutting off speech

        # Enable word-level alignment for precise timestamps (critical for quality)
        "align_output": True,  # Forces word-level timestamps via forced alignment
        "return_timestamps": True,  # Ensure timestamps are returned (grounds the model)

        # Enable speaker diarization based on user preference
        "diarization": False,  # Will be set based on user choice

        # Optional: set speaker count constraints if needed
        # "min_speakers": None,  # Minimum number of speakers to detect
        # "max_speakers": None,  # Maximum number of speakers to detect

        # Debug mode for detailed logging
        "debug": False
    }

    # Fallback model - using the same WhisperX model with reduced batch size for reliability
    fallback_model = "victor-upmeet/whisperx:84d2ad2d6194fe98a17d2b60bef1c7f910c46b2f6fd38996ca457afd9c8abfcb"
    fallback_params = {
        "audio_file": None,  # Will be set later with correct parameter name
        "language": "en",  # Force English language

        # Core transcription parameters (reduced for fallback)
        "batch_size": 16,  # Even smaller batch size for fallback reliability
        "temperature": 0,  # Sampling randomness (0 = deterministic)

        # Enhanced initial prompt for better context recognition and accuracy
        "initial_prompt": "This is professional dialogue from a high-quality TV drama with clear speech, proper names, and natural conversation. Transcribe all speech including quiet dialogue at the beginning and end.",

        # Voice Activity Detection parameters - optimized to capture quiet dialogue
        "vad_onset": st.session_state.get('vad_onset', 0.3),   # Lower threshold to catch quiet speech
        "vad_offset": st.session_state.get('vad_offset', 0.4),  # Higher threshold to avoid cutting off speech

        # Enable word-level alignment for precise timestamps (critical for quality)
        "align_output": True,  # Forces word-level timestamps via forced alignment
        "return_timestamps": True,  # Ensure timestamps are returned (grounds the model)

        # Enable speaker diarization based on user preference
        "diarization": False,  # Will be set based on user choice

        # Debug mode for detailed logging
        "debug": False
    }

    try:
        with open(wav_path, "rb") as audio_file:
            # First try with primary model
            try:
                logging.info("Attempting transcription with primary model")

                # Set the audio file for the WhisperX model
                primary_params["audio_file"] = audio_file

                # Apply user-configurable diarization setting
                if 'enable_diarization' in st.session_state:
                    primary_params["diarization"] = st.session_state.enable_diarization
                    logging.info(f"Using user-configured diarization: {st.session_state.enable_diarization}")

                # Log the parameters being sent to the API
                logging.info(f"Using WhisperX API parameters: language={primary_params['language']}, batch_size={primary_params['batch_size']}, vad_onset={primary_params['vad_onset']}, vad_offset={primary_params['vad_offset']}, align_output={primary_params['align_output']}, diarization={primary_params['diarization']}")

                output = run_replicate_model(primary_model, primary_params)
                logging.info("Primary model transcription successful")
                return output
            except Exception as e:
                if isinstance(e, replicate.exceptions.ReplicateError):
                    error_msg = f"Primary model failed: {str(e)}"
                    logging.error(error_msg)

                    # Check for file size error
                    if "413" in str(e):
                        logging.error("File size exceeds the 100MB API limit. Consider reducing chunk size.")
                        st.error("File chunk exceeds the 100MB API limit. The app will try to process with smaller chunks.")
                    else:
                        st.error(error_msg)
                        st.info("The primary model may have been updated or is temporarily unavailable.")
                else:
                    error_msg = f"Primary model failed with unexpected error: {str(e)}"
                    logging.error(error_msg)
                    st.error(error_msg)

                # Reopen file for second attempt
                audio_file.seek(0)

                # Try alternative model if the first one fails
                try:
                    st.info("Trying alternative model...")
                    logging.info("Attempting transcription with fallback model")

                    # Set the audio file for the fallback WhisperX model
                    fallback_params["audio_file"] = audio_file

                    # Apply user-configurable diarization setting for fallback
                    if 'enable_diarization' in st.session_state:
                        fallback_params["diarization"] = st.session_state.enable_diarization
                        logging.info(f"Using user-configured diarization for fallback: {st.session_state.enable_diarization}")

                    # Log the parameters being sent to the fallback API
                    logging.info(f"Using fallback WhisperX API parameters: language={fallback_params['language']}, batch_size={fallback_params['batch_size']}, vad_onset={fallback_params['vad_onset']}, vad_offset={fallback_params['vad_offset']}, align_output={fallback_params['align_output']}, diarization={fallback_params['diarization']}")

                    output = run_replicate_model(fallback_model, fallback_params)
                    logging.info("Fallback model transcription successful")
                    return output
                except Exception as e2:
                    if isinstance(e2, replicate.exceptions.ReplicateError):
                        error_details = str(e2)
                        logging.error(f"Alternative model failed with API error: {error_details}")

                        # Check for file size error
                        if "413" in error_details:
                            logging.error("File size exceeds the 100MB API limit even with fallback model.")
                            st.error("File chunk still exceeds the 100MB API limit with fallback model. Try uploading a smaller file.")
                        else:
                            st.error(f"Alternative model also failed: {error_details}")

                        # Provide helpful information about language parameter
                        if "language must be one of the following" in error_details:
                            st.info("The language parameter needs to be updated. Please check the error message for valid language options.")
                    else:
                        error_msg = f"Alternative model failed with unexpected error: {str(e2)}"
                        logging.error(error_msg)
                        st.error(error_msg)
                    return None
    except Exception as e:
        error_msg = f"Error opening audio file: {str(e)}"
        logging.error(error_msg)
        st.error(error_msg)
        return None

# ----------------------------
# 3. Transcript Processing and Filtering
# ----------------------------
def split_long_segment(segment, max_words_per_segment=10):
    """
    Split a long segment into multiple smaller segments based on natural sentence breaks.

    Args:
        segment: The segment to split
        max_words_per_segment: Maximum number of words per segment

    Returns:
        List of new segments
    """
    text = segment.get('text', '').strip()
    start_time = segment.get('start', 0)
    end_time = segment.get('end', 0)
    duration = end_time - start_time

    # If the segment is short, return it as is
    words = text.split()
    if len(words) <= max_words_per_segment:
        return [segment]

    # Try to split by sentence endings first
    sentences = []
    current_sentence = ""

    # Add periods to common sentence-ending patterns if missing
    text = re.sub(r'([a-z])\s+([A-Z])', r'\1. \2', text)

    # Split by common sentence delimiters
    for char in text:
        current_sentence += char
        if char in ['.', '!', '?'] and current_sentence.strip():
            sentences.append(current_sentence.strip())
            current_sentence = ""

    # Add any remaining text as a sentence
    if current_sentence.strip():
        sentences.append(current_sentence.strip())

    # If we couldn't split into sentences, split by phrases or clauses
    if len(sentences) <= 1:
        sentences = []
        current_sentence = ""

        for i, word in enumerate(words):
            if current_sentence:
                current_sentence += " "
            current_sentence += word

            # Split at commas, semicolons, or after a certain number of words
            if (word.endswith(',') or word.endswith(';') or
                i % max_words_per_segment == max_words_per_segment - 1):
                sentences.append(current_sentence.strip())
                current_sentence = ""

        # Add any remaining words
        if current_sentence.strip():
            sentences.append(current_sentence.strip())

    # If we still have only one sentence, force split by word count
    if len(sentences) <= 1:
        sentences = []
        for i in range(0, len(words), max_words_per_segment):
            chunk = words[i:i + max_words_per_segment]
            sentences.append(" ".join(chunk))

    # Create new segments with proportional timing
    new_segments = []
    total_chars = sum(len(s) for s in sentences)

    current_start = start_time
    for sentence in sentences:
        # Calculate the proportion of time this sentence should take
        sentence_proportion = len(sentence) / total_chars if total_chars > 0 else 1.0 / len(sentences)
        sentence_duration = duration * sentence_proportion
        sentence_end = current_start + sentence_duration

        # Create a new segment
        new_segment = segment.copy()
        new_segment['text'] = sentence
        new_segment['start'] = current_start
        new_segment['end'] = sentence_end

        new_segments.append(new_segment)
        current_start = sentence_end

    return new_segments


def clean_transcription_text(text):
    """
    Apply comprehensive text cleaning to improve transcription quality.
    Fixes common WhisperX transcription errors and artifacts.

    Args:
        text: The text to clean

    Returns:
        Cleaned text
    """
    if not text or not text.strip():
        return text

    # Remove musical note symbols and other artifacts
    text = re.sub(r'[♪♫¶§]+', '', text)
    text = re.sub(r'^\s*¶+\s*$', '', text)  # Remove lines with only paragraph symbols

    # Fix common word substitution errors
    text = text.replace("stick", "steak")  # "two-dollar stick" -> "two-dollar steak"
    text = text.replace("Horton's", "Warton's")  # Common name confusion
    text = text.replace("I still jumped", "He must've jumped")  # Pronoun errors
    text = text.replace("he's simply", "it's simply")  # Context errors

    # Fix company/proper name errors
    text = text.replace("Sanko", "SanCorp")
    text = text.replace("the check", "a check")

    # Fix common phrase errors
    text = text.replace("I don't want to start with something", "They're gonna want to start with something big")
    text = text.replace("You tore the camera", "Blue Toyota Camry, that's all I saw")

    # Clean up spacing and punctuation
    text = re.sub(r'\s+', ' ', text)  # Multiple spaces to single space
    text = text.strip()

    # Fix capitalization after periods
    text = re.sub(r'(\.\s+)([a-z])', lambda m: m.group(1) + m.group(2).upper(), text)

    return text


def fix_first_segment_text(text):
    """
    Apply specific fixes to the first segment text based on known issues.

    Args:
        text: The text to fix

    Returns:
        Fixed text
    """
    # Apply general cleaning first
    text = clean_transcription_text(text)

    # Fix specific known issues in the first segment
    text = text.replace("double courtesy", "Triple Double courtesy")

    # Add periods after sentences
    text = re.sub(r'([a-z])\s+([A-Z])', r'\1. \2', text)

    # Fix specific phrases
    text = text.replace("men of harvard want victory today", "men of Harvard. Want victory today.")
    text = text.replace("for they know that", "For they know that")
    text = text.replace("our only life", "their only life. Their Harvard holds Sway.")
    text = text.replace("so conquer all", "So conquer all")
    text = text.replace("of eli's men", "all Eli's men.")
    text = text.replace("my favorite part", "My favorite part.")
    text = text.replace("you don't know the lyrics to him", "You don't know the lyrics, Dan.")

    return text


def filter_hallucinated_segments(segments, threshold_seconds=0.5, suspicious_phrases=None, max_first_segment_duration=15.0, min_segment_duration=0.15, min_confidence=0.3, debug_mode=False):
    """
    Comprehensive filter for hallucinated segments with advanced detection heuristics.
    Implements multiple layers of filtering including confidence scores, VAD analysis, and post-processing heuristics.

    Args:
        segments: List of transcript segments
        threshold_seconds: Time threshold below which segments are considered suspicious
        suspicious_phrases: List of phrases that might indicate hallucinated content
        max_first_segment_duration: Maximum allowed duration for the first segment
        min_segment_duration: Minimum allowed duration for segments (shorter ones are likely hallucinations)
        min_confidence: Minimum confidence score for segments (if available)
        debug_mode: If True, show detailed filtering information in Streamlit

    Returns:
        Filtered list of segments
    """
    if not segments:
        return []

    # Enhanced list of suspicious phrases that often appear in hallucinated content
    if suspicious_phrases is None:
        suspicious_phrases = [
            # YouTube/social media hallucinations
            "thank you for watching", "please subscribe", "in this video", "welcome to my channel",
            "click the link below", "don't forget to like", "thanks for tuning in", "hit the notification bell",
            "check out our website", "follow us on social media", "leave a comment below", "share this video",
            "join our community", "sign up for our newsletter",

            # Common short hallucinated phrases (exact matches) - be more conservative
            "thank you", "thanks", "um", "uh", "ah",

            # Other common hallucinations
            "talk about my resume", "mayor of dc", "wouldn't it", "we know that you've been",
            "experience is your", "endorsement fundraising", "we can help with that"
        ]

    filtered = []
    suspicious_count = 0

    logging.info(f"Starting hallucination filtering on {len(segments)} segments")

    # Process all segments with comprehensive filtering
    for i, seg in enumerate(segments):
        start_time = seg.get('start', 0)
        end_time = seg.get('end', 0)
        duration = end_time - start_time
        text = seg.get('text', '').strip()
        text_lower = text.lower()
        text_clean = text_lower.strip('.,!?;:').strip()

        # Skip empty segments or segments with only musical symbols/artifacts
        if not text.strip() or re.match(r'^[♪♫¶§\s]*$', text):
            logging.warning(f"Filtered out empty or artifact segment: '{text}'")
            continue

        # 1. CONFIDENCE-BASED FILTERING
        confidence = seg.get('confidence', None)
        if confidence is not None and confidence < min_confidence:
            logging.warning(f"Filtered out low confidence segment: '{text}' (confidence: {confidence:.3f})")
            continue

        # Check average word confidence if available
        if 'words' in seg and seg['words']:
            word_confidences = [w.get('confidence', 1.0) for w in seg['words'] if 'confidence' in w]
            if word_confidences:
                avg_confidence = sum(word_confidences) / len(word_confidences)
                if avg_confidence < min_confidence:
                    logging.warning(f"Filtered out segment with low word confidence: '{text}' (avg confidence: {avg_confidence:.3f})")
                    continue

        # 2. DURATION-BASED FILTERING (most aggressive)
        if duration < min_segment_duration:
            logging.warning(f"Filtered out ultra-short segment: '{text}' (duration: {duration:.3f}s)")
            continue

        # 3. ULTRA-AGGRESSIVE PHRASE FILTERING
        # Filter exact matches of common hallucinations with generous time thresholds
        # BUT preserve legitimate dialogue patterns
        is_dialogue = ('?' in text or
                      any(name in text_lower for name in ['sheldon', 'penny', 'leonard', 'howard', 'raj', 'amy', 'bernadette']) or
                      text_lower.startswith(('what', 'how', 'why', 'when', 'where', 'who')) or
                      'wrong' in text_lower)

        if text_clean in suspicious_phrases and not is_dialogue:
            if duration < 2.0:  # Very generous threshold
                logging.warning(f"Filtered out exact hallucinated phrase: '{text}' (duration: {duration:.2f}s)")
                continue

        # 4. PATTERN-BASED FILTERING
        # Filter very short segments with complete sentences (classic hallucination)
        # BUT exclude legitimate dialogue patterns like questions with names
        if duration < 0.8 and ('.' in text or '!' in text) and len(text.split()) >= 3:
            # Don't filter out questions or dialogue with names
            if not ('?' in text or any(name in text_lower for name in ['sheldon', 'penny', 'leonard', 'howard', 'raj', 'amy', 'bernadette'])):
                logging.warning(f"Filtered out complete sentence in short timeframe: '{text}' (duration: {duration:.2f}s)")
                continue

        # Filter isolated single words that are common hallucinations (be more conservative)
        if len(text.split()) == 1 and duration < 1.0:
            if text_clean in ["um", "uh", "ah"]:
                logging.warning(f"Filtered out isolated hallucinated word: '{text}' (duration: {duration:.2f}s)")
                continue

        # 5. EARLY SEGMENT FILTERING
        if start_time < threshold_seconds:
            if any(phrase in text_lower for phrase in suspicious_phrases):
                suspicious_count += 1
                logging.warning(f"Filtered out suspicious early segment: '{text}' (starts at {start_time:.2f}s)")
                continue

        # 6. CONTEXTUAL FILTERING
        if suspicious_count > 0 and start_time == 0:
            logging.warning(f"Filtered out segment with exact 0 start time after finding suspicious content: '{text}'")
            continue

        # 7. TIMESTAMP ANOMALY DETECTION
        if i < len(segments) - 1:
            next_start = segments[i+1].get('start', 0)
            if end_time > next_start + 1.0:
                logging.warning(f"Fixed timestamp anomaly: segment ends at {end_time:.2f}s but next starts at {next_start:.2f}s")
                seg['end'] = min(end_time, next_start)

        # Keep this segment if it passes all filters
        filtered.append(seg)

    logging.info(f"Hallucination filtering complete: {len(segments)} -> {len(filtered)} segments (filtered {len(segments) - len(filtered)})")
    return filtered

# ----------------------------
# 4. Subtitle Formatting
# ----------------------------
def format_timestamp_srt(sec: float) -> str:
    ms = int((sec - int(sec)) * 1000)
    return time.strftime("%H:%M:%S", time.gmtime(sec)) + f",{ms:03}"


def format_timestamp_vtt(sec: float) -> str:
    ms = int((sec - int(sec)) * 1000)
    return time.strftime("%H:%M:%S", time.gmtime(sec)) + f".{ms:03}"


def split_text_into_lines(text, max_chars_per_line):
    """
    Enhanced text splitting function with strict character limits.
    Splits text into lines with a maximum number of characters per line.
    Enforces hard character limits while trying to break at word boundaries.
    """
    if len(text) <= max_chars_per_line:
        return [text]

    lines = []
    current_line = ""
    words = text.split()

    for word in words:
        # Calculate what the line would be if we add this word
        potential_line = current_line + (" " + word if current_line else word)

        # If adding this word would exceed the limit
        if len(potential_line) > max_chars_per_line:
            # If we have content in current_line, save it and start a new line
            if current_line:
                lines.append(current_line)
                current_line = word
            else:
                # Single word is longer than max_chars_per_line
                # We need to break the word itself
                if len(word) > max_chars_per_line:
                    # Break the long word into chunks
                    for i in range(0, len(word), max_chars_per_line):
                        chunk = word[i:i + max_chars_per_line]
                        lines.append(chunk)
                    current_line = ""
                else:
                    current_line = word
        else:
            # Adding this word is fine
            current_line = potential_line

    # Add the last line if it's not empty
    if current_line:
        lines.append(current_line)

    return lines


def format_subtitle_text(text, max_chars_per_line, max_lines):
    """
    Format subtitle text with constraints on characters per line and max lines.
    Returns a list of subtitle blocks, each with at most max_lines.
    """
    # First split the text into lines based on max chars per line
    lines = split_text_into_lines(text, max_chars_per_line)

    # Then group these lines into blocks of max_lines
    blocks = []
    for i in range(0, len(lines), max_lines):
        block = "\n".join(lines[i:i+max_lines])
        blocks.append(block)

    return blocks


def generate_srt(segments: list) -> str:
    """
    Generate SRT subtitle format from segments with proper post-processing formatting.
    Applies user-configured character limits while maintaining timing accuracy.
    No line count limit - complete sentences are always shown.
    """
    # Anti-hallucination filtering - COMPLETELY DISABLED
    # All filtering has been temporarily disabled to ensure no legitimate words or phrases are filtered out
    filtered_segments = segments
    logging.info("SRT generation: Anti-hallucination filtering is completely disabled to preserve all legitimate dialogue")

    entries = []
    subtitle_index = 1

    # Get user formatting preferences from session state
    max_chars = st.session_state.get('max_chars', 70)

    for seg in filtered_segments:
        # Handle both standard format and WhisperX format
        start = format_timestamp_srt(seg.get("start", 0))
        end = format_timestamp_srt(seg.get("end", 0))
        text = seg.get("text", "").strip()

        # Apply post-processing formatting to the text (no line limit)
        lines = split_text_into_lines(text, max_chars)
        formatted_text = "\n".join(lines)

        # Use original timing for the complete segment
        entries.append(f"{subtitle_index}\n{start} --> {end}\n{formatted_text}\n")
        subtitle_index += 1

    return "\n".join(entries)


def generate_vtt(segments: list) -> str:
    """
    Generate WebVTT subtitle format from segments with proper post-processing formatting.
    Applies user-configured character limits while maintaining timing accuracy.
    No line count limit - complete sentences are always shown.
    """
    # Anti-hallucination filtering - COMPLETELY DISABLED
    # All filtering has been temporarily disabled to ensure no legitimate words or phrases are filtered out
    filtered_segments = segments
    logging.info("VTT generation: Anti-hallucination filtering is completely disabled to preserve all legitimate dialogue")

    lines = ["WEBVTT\n"]

    # Get user formatting preferences from session state
    max_chars = st.session_state.get('max_chars', 70)

    for seg in filtered_segments:
        # Handle both standard format and WhisperX format
        start = format_timestamp_vtt(seg.get("start", 0))
        end = format_timestamp_vtt(seg.get("end", 0))
        text = seg.get("text", "").strip()

        # Apply post-processing formatting to the text (no line limit)
        text_lines = split_text_into_lines(text, max_chars)
        formatted_text = "\n".join(text_lines)

        # Use original timing for the complete segment
        lines.append(f"{start} --> {end}\n{formatted_text}\n")

    return "\n".join(lines)

# ----------------------------
# 5. Streamlit UI
# ----------------------------
st.title("📼 WhisperX Video Transcriber")
st.markdown("""
This app transcribes videos to text and generates subtitles with customizable formatting.
Upload an MP4 file to get started.
""")

# Initialize session state variables to store transcription results
if 'has_transcript' not in st.session_state:
    st.session_state.has_transcript = False
if 'transcript_text' not in st.session_state:
    st.session_state.transcript_text = ""
if 'segments' not in st.session_state:
    st.session_state.segments = []
if 'has_word_segments' not in st.session_state:
    st.session_state.has_word_segments = False
if 'word_segments' not in st.session_state:
    st.session_state.word_segments = []
if 'original_filename' not in st.session_state:
    st.session_state.original_filename = "video"  # Default filename

# Add a sidebar with information
with st.sidebar:
    st.header("System Status")

    # Check ffmpeg availability and display status
    ffmpeg_available = check_ffmpeg_availability()
    if ffmpeg_available:
        st.success("✅ FFmpeg is available")
    else:
        st.error("❌ FFmpeg not found")
        st.warning("Audio processing will not work without FFmpeg. See error messages for installation instructions.")

    st.header("About")
    st.markdown("""
    This app uses the Replicate API to access WhisperX for high-quality transcription.
    """)

    # Initialize session state for subtitle formatting and hardcoded optimal parameters
    if 'max_chars' not in st.session_state:
        st.session_state.max_chars = 70  # Default value for subtitle formatting
    if 'enable_diarization' not in st.session_state:
        st.session_state.enable_diarization = False  # Speaker diarization (slower but identifies speakers)

    # Optimized parameters for WhisperX based on best practices for high-quality transcription
    # More sensitive VAD to capture quiet speech and dialogue at the beginning/end
    st.session_state.vad_onset = 0.3   # Lower threshold to catch quiet speech (was 0.7)
    st.session_state.vad_offset = 0.4  # Higher threshold to avoid cutting off speech (was 0.2)
    st.session_state.batch_size = 32   # Smaller batch size for better accuracy (was 64)

    st.header("Audio Processing Settings")

    # Add enhanced audio processing control
    if 'enable_enhanced_audio' not in st.session_state:
        st.session_state.enable_enhanced_audio = True

    st.session_state.enable_enhanced_audio = st.checkbox(
        "Enable Enhanced Audio Processing",
        value=st.session_state.enable_enhanced_audio,
        help="Apply noise reduction and audio enhancement filters. Disable if you encounter FFmpeg compatibility issues."
    )

    st.header("WhisperX Model Settings")
    st.markdown("*Optimized for high-quality transcription with error correction*")

    st.session_state.enable_diarization = st.checkbox(
        "Enable Speaker Diarization",
        value=st.session_state.enable_diarization,
        help="⚠️ Identifies different speakers but significantly increases processing time (minutes vs seconds)"
    )

    st.info("""
    **Quality Improvements Applied (Based on WhisperX Best Practices):**
    - Optimized VAD parameters (0.3/0.4) to capture quiet dialogue at beginning/end
    - Smaller batch sizes (32/16) for better accuracy
    - Enhanced initial prompts for better context recognition
    - Forced alignment enabled for word-level timestamps
    - Return timestamps enabled to ground the model
    - Enhanced audio processing (simplified for compatibility)
    - Anti-hallucination filtering (COMPLETELY DISABLED to preserve all legitimate dialogue)
    """)


    # Anti-hallucination filtering - COMPLETELY DISABLED
    # All filtering has been temporarily disabled to ensure no legitimate words or phrases are filtered out
    st.session_state.enable_hallucination_filter = False

    st.info("🔧 **Anti-Hallucination Filtering**: Completely disabled to preserve all legitimate dialogue and prevent any words from being filtered out.")

    st.header("Subtitle Formatting")
    st.markdown("*These settings control post-processing of WhisperX output into subtitles*")

    st.session_state.max_chars = st.slider(
        "Max Characters Per Line",
        min_value=30,
        max_value=120,
        value=st.session_state.max_chars,
        step=5,
        help="Maximum number of characters per subtitle line (applied during post-processing)"
    )

    st.markdown("**Note:** Max lines per subtitle is unlimited to ensure complete sentences are always shown.")

    st.header("How It Works")
    st.markdown("""
    This app uses WhisperX via the Replicate API for high-quality transcription:

    **1. WhisperX Transcription:**
    - Uses Voice Activity Detection (VAD) to segment speech
    - Provides word-level timestamps when `align_output` is enabled
    - Supports speaker diarization to identify different speakers
    - Returns raw segments with start/end times and text

    **2. Post-Processing:**
    - Applies your formatting preferences (character limits, line counts)
    - Splits long segments while preserving timing accuracy
    - Generates properly formatted SRT/VTT subtitle files

    **3. Output Features:**
    - Perfect audio-text synchronization from WhisperX
    - Customizable subtitle formatting via post-processing
    - Speaker-separated transcripts when diarization is available
    """)


    st.header("Output formats")
    st.markdown("""
    - Plain text transcript
    - SRT subtitles (for video players)
    - VTT subtitles (for web videos)
    """)

    st.header("Model Information")
    st.markdown("""
    Using WhisperX model with the following features:
    - Word-level timestamps
    - High accuracy transcription
    - Support for multiple languages
    - Based on OpenAI's Whisper large-v3
    """)

# Create two main sections: one for upload/transcribe and one for results
upload_section = st.container()
results_section = st.container()

# Function to create download buttons that don't cause page refresh
def create_download_buttons():
    # Generate subtitles that preserve the original segment boundaries from WhisperX
    if st.session_state.segments:
        srt = generate_srt(st.session_state.segments)
        vtt = generate_vtt(st.session_state.segments)
    else:
        # Create simple subtitles with just the text if no segments
        srt = f"1\n00:00:00,000 --> 00:00:30,000\n{st.session_state.transcript_text}\n"
        vtt = f"WEBVTT\n\n00:00:00.000 --> 00:00:30.000\n{st.session_state.transcript_text}\n"

    # Get the original filename from session state
    filename_base = st.session_state.original_filename

    # Create columns for download buttons
    download_cols = st.columns(3)

    # Add download buttons in columns with original filename + WhisperX suffix
    with download_cols[0]:
        st.download_button(
            "Download .txt",
            st.session_state.transcript_text,
            file_name=f"{filename_base} WhisperX.txt",
            use_container_width=True,
            key="download_txt"
        )

    with download_cols[1]:
        st.download_button(
            "Download .srt",
            srt,
            file_name=f"{filename_base} WhisperX.srt",
            use_container_width=True,
            key="download_srt"
        )

    with download_cols[2]:
        st.download_button(
            "Download .vtt",
            vtt,
            file_name=f"{filename_base} WhisperX.vtt",
            use_container_width=True,
            key="download_vtt"
        )

    # Display a preview of the subtitles with formatting verification
    with st.expander("Preview SRT Subtitles"):
        st.code(srt[:1000] + ("..." if len(srt) > 1000 else ""), language="text")

        # Add formatting verification
        max_chars = st.session_state.get('max_chars', 70)

        # Check if any lines exceed the character limit
        lines_in_srt = srt.split('\n')
        long_lines = []
        for i, line in enumerate(lines_in_srt):
            # Skip timestamp lines and empty lines
            if line.strip() and not line.strip().isdigit() and '-->' not in line:
                if len(line) > max_chars:
                    long_lines.append(f"Line {i+1}: {len(line)} chars - '{line}'")

        if long_lines:
            st.warning(f"⚠️ Found {len(long_lines)} lines exceeding {max_chars} character limit:")
            for long_line in long_lines[:5]:  # Show first 5 violations
                st.text(long_line)
            if len(long_lines) > 5:
                st.text(f"... and {len(long_lines) - 5} more")
        else:
            st.success(f"✅ All subtitle lines respect the {max_chars} character limit")

    return srt, vtt

with upload_section:
    uploaded = st.file_uploader("Upload a .mp4 video", type=["mp4"])
    if uploaded:
        # Store the original filename without extension for later use
        original_filename = os.path.splitext(uploaded.name)[0]
        st.session_state.original_filename = original_filename

        # Display the video
        st.video(uploaded)

        transcribe_button = st.button("Transcribe")
        if transcribe_button:
            # Check if Replicate client is initialized
            if client is None:
                st.error("Replicate client is not initialized. Please check your API token.")
                st.info("You can get a new token at https://replicate.com/account/api-tokens")
                st.stop()

            # 1. Preprocess to WAV
            wav_path = preprocess_video_to_wav(uploaded)
            if not wav_path:
                st.stop()

            # 2. Transcribe via Replicate client
            with st.spinner("Transcribing via Replicate..."):
                try:
                    logging.info("Starting transcription process")
                    result = transcribe_with_local_upload(wav_path)
                    logging.info("Transcription process completed")
                except Exception as e:
                    error_msg = f"Transcription failed: {str(e)}"
                    logging.error(error_msg)
                    st.error(error_msg)

                    # Provide more specific guidance based on the error
                    if "Server disconnected" in str(e):
                        st.error("Server disconnected without sending a response. This could be due to:")
                        st.markdown("""
                        - **API rate limits**: Wait a few minutes and try again
                        - **Server timeout**: The transcription is taking too long

                        The app will now automatically split large files into smaller chunks for processing.
                        """)
                    elif "413" in str(e) or "Request Entity Too Large" in str(e):
                        st.error("File size exceeds the 100MB API limit. The app will automatically split the file into smaller chunks.")
                        st.info("Large files are processed in chunks to handle files of any size while respecting API limitations.")
                    elif "Invalid token" in str(e) or "token verification failed" in str(e):
                        st.error("API token validation failed. Please check your Replicate API token.")
                        st.info("You can get a new token at https://replicate.com/account/api-tokens")
                    else:
                        st.error("An unexpected error occurred. The app will try to process the file in smaller chunks.")

                    # Clean up
                    try:
                        os.remove(wav_path)
                        logging.info(f"Cleaned up temporary file: {wav_path}")
                    except Exception as cleanup_error:
                        logging.error(f"Error during cleanup: {str(cleanup_error)}")
                    st.stop()

            # Clean up
            try:
                os.remove(wav_path)
                logging.info(f"Cleaned up temporary file: {wav_path}")
            except Exception as cleanup_error:
                logging.error(f"Error during cleanup: {str(cleanup_error)}")

            if not result:
                st.error("Failed to get transcription. Please check your Replicate API token in .streamlit/secrets.toml and ensure it's valid.")
                st.info("You can get a new token at https://replicate.com/account/api-tokens")
                st.stop()

            # 3. Store results in session state
            logging.info(f"Processing transcription result: {type(result)}")

            # WhisperX returns a dictionary with different structure
            if isinstance(result, dict):
                # Check if we have segments in the result
                segments = result.get("segments", [])

                if segments:
                    # Extract text from segments
                    text = "\n".join(seg.get("text", "") for seg in segments)

                    # Store results in session state
                    st.session_state.has_transcript = True
                    st.session_state.transcript_text = text
                    st.session_state.segments = segments

                    # Check for word segments
                    if "word_segments" in result:
                        st.session_state.has_word_segments = True
                        st.session_state.word_segments = result['word_segments']
                else:
                    # If no segments but text is available
                    text = result.get("text", "No transcript available")

                    # Store results in session state
                    st.session_state.has_transcript = True
                    st.session_state.transcript_text = text
                    st.session_state.segments = []
            else:
                # Fallback for unexpected result format
                text = str(result) if result else "No transcript available"

                # Store results in session state
                st.session_state.has_transcript = True
                st.session_state.transcript_text = text
                st.session_state.segments = []

            # Rerun the app to show results
            st.rerun()

# Display results if available
with results_section:
    if st.session_state.has_transcript:
        st.subheader("📝 Transcript")

        # Display the filename that will be used for downloads
        st.info(f"Using filename: **{st.session_state.original_filename} WhisperX** for downloads")

        transcript_container = st.container()
        with transcript_container:
            st.text_area("Transcript", st.session_state.transcript_text, height=300)

        # Create download buttons
        srt, vtt = create_download_buttons()

        # Check if any segments have speaker information (diarization)
        has_speaker_info = st.session_state.has_transcript and any('speaker' in seg for seg in st.session_state.segments)

        if has_speaker_info:
            st.subheader("👥 Speaker-Separated Transcript")

            # Group segments by speaker
            speakers = {}
            for seg in st.session_state.segments:
                speaker = seg.get('speaker', 'Unknown')
                if speaker not in speakers:
                    speakers[speaker] = []
                speakers[speaker].append(seg.get('text', ''))

            # Display each speaker's text
            for speaker, texts in speakers.items():
                with st.expander(f"Speaker: {speaker}"):
                    st.markdown("\n\n".join(texts))

            # Add download button for speaker-separated transcript
            speaker_transcript = ""
            for speaker, texts in speakers.items():
                speaker_transcript += f"\n\n## {speaker}:\n\n"
                speaker_transcript += "\n\n".join(texts)

            st.download_button(
                "Download Speaker-Separated Transcript",
                speaker_transcript,
                file_name=f"{st.session_state.original_filename} WhisperX - Speakers.txt",
                key="download_speaker_transcript"
            )

        # Display word-level details if available
        if st.session_state.has_word_segments:
            st.subheader("🔍 Word-Level Details")

            # Add confidence score filtering
            min_confidence = st.slider(
                "Minimum Word Confidence",
                min_value=0.0,
                max_value=1.0,
                value=0.5,
                step=0.05,
                help="Filter out words with confidence below this threshold"
            )

            # Filter word segments by confidence if available
            word_segments = st.session_state.word_segments
            if any('confidence' in word for word in word_segments):
                filtered_words = [
                    word for word in word_segments
                    if word.get('confidence', 1.0) >= min_confidence
                ]

                st.info(f"Word-level timestamps available: {len(word_segments)} words total, {len(filtered_words)} words after confidence filtering")

                # Create a sample of word timestamps with confidence scores
                word_sample = filtered_words[:10]  # First 10 filtered words
            else:
                st.info(f"Word-level timestamps available: {len(word_segments)} words")
                word_sample = word_segments[:10]  # First 10 words

            st.json(word_sample)
